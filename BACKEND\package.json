{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "type": "module", "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "nanoid": "^5.1.5", "nodemon": "^3.1.10", "path-to-regexp": "^8.2.0", "url_shortner": "file:.."}}